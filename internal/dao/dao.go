package dao

import (
	"context"
	"recommend-base/internal/model/mrecommendedexposurelevel"
	"time"

	"go-common/library/cache/redis"
	"go-common/library/conf/paladin.v2"
	"go-common/library/database/sql"
	"go-common/library/sync/pipeline/fanout"
	xtime "go-common/library/time"

	"github.com/google/wire"
)

var Provider = wire.NewSet(New, NewDB, NewRedis, NewMClient)

// Dao dao interface
//
//go:generate kratos tool btsgen
type Dao interface {
	Close()
	Ping(ctx context.Context) (err error)

	// bts: -nullcache=&RoomList{Pagination:&Pagination{Count:-1}} -check_null_code=$!=nil&&$.Pagination.Count==-1
	LivingRoomsInfo(ctx context.Context, page int64, pageSize int64) (*RoomList, error)
	// bts: -nullcache=&RoomExtraInfoList{List:[]*RoomExtraInfo{{RoomID:-1}}} -check_null_code=$!=nil&&len($.List)!=0&&$.List[0].RoomID==-1
	RoomsExtraInfo(ctx context.Context, params RoomsExtraInfoParams) (*RoomExtraInfoList, error)

	// bts: -nullcache=[]*mrecommendedexposurelevel.Model{{ID:-1}} -check_null_code=$!=nil&&len($)!=0&&$[0].ID==-1
	GetRecommendedExposureLevels(ctx context.Context) ([]*mrecommendedexposurelevel.Model, error)
	// bts: -nullcache=&InterventionCardList{List:[]*InterventionCard{{CardID:-1}}} -check_null_code=$!=nil&&len($.List)!=0&&$.List[0].CardID==-1
	GetHomeFeedInterventionCards(ctx context.Context, page int64, pageSize int64) (*InterventionCardList, error)
	// bts: -nullcache=&InterventionCardList{List:[]*InterventionCard{{CardID:-1}}} -check_null_code=$!=nil&&len($.List)!=0&&$.List[0].CardID==-1
	GetLivePageInterventionCards(ctx context.Context, page int64, pageSize int64) (*InterventionCardList, error)
}

// dao dao.
type dao struct {
	db          *sql.DB
	mclient     *MClient
	redis       *redis.Redis
	cache       *fanout.Fanout
	cacheExpire int32
}

// New new a dao and return.
func New(r *redis.Redis, mclient *MClient, db *sql.DB) (d Dao, cf func(), err error) {
	return newDao(r, mclient, db)
}

func newDao(r *redis.Redis, mclient *MClient, db *sql.DB) (d *dao, cf func(), err error) {
	var cfg struct {
		CacheExpire xtime.Duration
	}

	if err = paladin.Get("application.toml").UnmarshalTOML(&cfg); err != nil {
		return
	}
	d = &dao{
		db:          db,
		mclient:     mclient,
		redis:       r,
		cache:       fanout.New("cache"),
		cacheExpire: int32(time.Duration(cfg.CacheExpire) / time.Second),
	}
	cf = d.Close
	return
}

// Close close the resource.
func (d *dao) Close() {
	d.cache.Close()
}

// Ping ping the resource.
func (d *dao) Ping(ctx context.Context) (err error) {
	return nil
}
