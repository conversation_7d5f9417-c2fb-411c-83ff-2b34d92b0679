package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"recommend-base/internal/model/mrecommendedexposurelevel"
	"sort"
	"strconv"

	"go-common/library/cache/redis"
	"go-common/library/log"
)

func keyLivingRoomsInfo(page, pageSize int64) string {
	return fmt.Sprintf("living_rooms_info_%d_%d", page, pageSize)
}

// CacheLivingRoomsInfo get data from cache
func (d *dao) CacheLivingRoomsInfo(ctx context.Context, page int64, pageSize int64) (rooms *RoomList, err error) {
	key := keyLivingRoomsInfo(page, pageSize)
	r, err := d.redis.Do(ctx, "GET", key)
	if err != nil {
		if err == redis.ErrNil {
			err = nil
		}
	}
	if err == nil && r != nil {
		err = json.Unmarshal(r.([]byte), &rooms)
		// treat marshal error as cache not found to create new cache
		if err != nil {
			log.Error("CacheLivingRoomsInfo error(%v), key(%s)", err, key)
			rooms = nil
			err = nil
		}
	}
	if err != nil {
		rooms = nil
		log.Error("CacheLivingRoomsInfo error(%v), key(%s)", err, key)
		return
	}

	return
}

// AddCacheLivingRoomsInfo add data to cache
func (d *dao) AddCacheLivingRoomsInfo(ctx context.Context, page int64, rooms *RoomList, pageSize int64) (err error) {
	if rooms == nil {
		return
	}

	var data []byte
	key := keyLivingRoomsInfo(page, pageSize)
	data, err = json.Marshal(rooms)
	if err != nil {
		log.Error("AddCacheLivingRoomsInfo error(%v), key(%s)", err, key)
		return
	}
	if _, err = d.redis.Do(ctx, "SETEX", key, int(d.cacheExpire), string(data)); err != nil {
		log.Error("AddCacheLivingRoomsInfo error(%v), key(%s)", err, key)
		return
	}

	return
}

// DeleteLivingRoomsInfoCache delete data from cache
func (d *dao) DeleteLivingRoomsInfoCache(ctx context.Context, page int64, pageSize int64) (err error) {
	key := keyLivingRoomsInfo(page, pageSize)
	if _, err = d.redis.Do(ctx, "DEL", key); err != nil {
		if err == redis.ErrNil {
			err = nil
			return
		}
		log.Error("DeleteLivingRoomsInfoCache error(%v), key(%s)", err, key)
		return
	}

	return
}

func keyRoomsExtraInfo(roomIDs []int64) string {
	// 对数组里的直播间 ID 先排序，然后再拼接成 key
	sort.Slice(roomIDs, func(i, j int) bool {
		return roomIDs[i] < roomIDs[j]
	})
	var roomIDStr string
	for i, dramaID := range roomIDs {
		if i > 0 {
			roomIDStr += ","
		}
		roomIDStr += strconv.FormatInt(dramaID, 10)
	}
	return "rooms_extra_info_roomids_" + roomIDStr
}

// CacheRoomsExtraInfo get data from redis
func (d *dao) CacheRoomsExtraInfo(ctx context.Context, params RoomsExtraInfoParams) (rooms *RoomExtraInfoList, err error) {
	if len(params.RoomIDs) == 0 {
		return
	}
	key := keyRoomsExtraInfo(params.RoomIDs)
	r, err := d.redis.Do(ctx, "GET", key)
	if err != nil {
		if err == redis.ErrNil {
			err = nil
		}
	}

	if err == nil && r != nil {
		rooms = new(RoomExtraInfoList)
		err = json.Unmarshal(r.([]byte), &rooms.List)
		// treat marshal error as cache not found to create new cache
		if err != nil {
			log.Error("CacheFeedCardDramaInfo error(%v), key(%s)", err, key)
			rooms = nil
			err = nil
		}
	}
	if err != nil {
		rooms = nil
		log.Error("CacheFeedCardDramaInfo error(%v) key(%s)", err, key)
		return
	}

	return
}

// AddCacheRoomsExtraInfo Set data to redis
func (d *dao) AddCacheRoomsExtraInfo(ctx context.Context, params RoomsExtraInfoParams, rooms *RoomExtraInfoList) (err error) {
	if rooms == nil || len(params.RoomIDs) == 0 {
		return
	}

	var data []byte
	key := keyRoomsExtraInfo(params.RoomIDs)
	data, err = json.Marshal(rooms.List)
	if err != nil {
		log.Error("AddCacheRoomsExtraInfo err(%s), key(%s)", err, key)
		return
	}
	if _, err = d.redis.Do(ctx, "SETEX", key, int(d.cacheExpire), string(data)); err != nil {
		log.Error("AddCacheRoomsExtraInfo err(%s), key(%s)", err, key)
		return
	}

	return
}

// DeleteRoomsExtraInfoCache delete data from redis
func (d *dao) DeleteRoomsExtraInfoCache(ctx context.Context, params RoomsExtraInfoParams) (err error) {
	if len(params.RoomIDs) == 0 {
		return
	}

	key := keyRoomsExtraInfo(params.RoomIDs)
	if _, err = d.redis.Do(ctx, "DEL", key); err != nil {
		if err == redis.ErrNil {
			err = nil
			return
		}
		log.Error("DeleteRoomsExtraInfoCache error(%v), key(%s)", err, key)
		return
	}

	return
}

func keyHomeFeedInterventionCards(page, pageSize int64) string {
	return fmt.Sprintf("home_feed_intervention_cards_%d_%d", page, pageSize)
}

// CacheGetHomeFeedInterventionCards get data from cache
func (d *dao) CacheGetHomeFeedInterventionCards(ctx context.Context, page int64, pageSize int64) (cards *InterventionCardList, err error) {
	key := keyHomeFeedInterventionCards(page, pageSize)
	r, err := d.redis.Do(ctx, "GET", key)
	if err != nil {
		if err == redis.ErrNil {
			err = nil
		}
	}
	if err == nil && r != nil {
		err = json.Unmarshal(r.([]byte), &cards)
		if err != nil {
			log.Error("CacheGetHomeFeedInterventionCards error(%v), key(%s)", err, key)
			cards = nil
			err = nil
		}
	}
	if err != nil {
		cards = nil
		log.Error("CacheGetHomeFeedInterventionCards error(%v), key(%s)", err, key)
		return
	}
	return
}

// AddCacheGetHomeFeedInterventionCards add data to cache
func (d *dao) AddCacheGetHomeFeedInterventionCards(ctx context.Context, page int64, cards *InterventionCardList, pageSize int64) (err error) {
	if cards == nil {
		return
	}
	key := keyHomeFeedInterventionCards(page, pageSize)
	data, err := json.Marshal(cards)
	if err != nil {
		log.Error("AddCacheGetHomeFeedInterventionCards error(%v), key(%s)", err, key)
		return
	}
	if _, err = d.redis.Do(ctx, "SETEX", key, int(d.cacheExpire), string(data)); err != nil {
		log.Error("AddCacheGetHomeFeedInterventionCards error(%v), key(%s)", err, key)
		return
	}
	return
}

func keyLivePageInterventionCards(page, pageSize int64) string {
	return fmt.Sprintf("live_page_intervention_cards_%d_%d", page, pageSize)
}

// CacheGetLivePageInterventionCards get data from cache
func (d *dao) CacheGetLivePageInterventionCards(ctx context.Context, page int64, pageSize int64) (cards *InterventionCardList, err error) {
	key := keyLivePageInterventionCards(page, pageSize)
	r, err := d.redis.Do(ctx, "GET", key)
	if err != nil {
		if err == redis.ErrNil {
			err = nil
		}
	}
	if err == nil && r != nil {
		err = json.Unmarshal(r.([]byte), &cards)
		if err != nil {
			log.Error("CacheGetLivePageInterventionCards error(%v), key(%s)", err, key)
			cards = nil
			err = nil
		}
	}
	if err != nil {
		cards = nil
		log.Error("CacheGetLivePageInterventionCards error(%v), key(%s)", err, key)
		return
	}
	return
}

// AddCacheGetLivePageInterventionCards add data to cache
func (d *dao) AddCacheGetLivePageInterventionCards(ctx context.Context, page int64, cards *InterventionCardList, pageSize int64) (err error) {
	if cards == nil {
		return
	}
	key := keyLivePageInterventionCards(page, pageSize)
	data, err := json.Marshal(cards)
	if err != nil {
		log.Error("AddCacheGetLivePageInterventionCards error(%v), key(%s)", err, key)
		return
	}
	if _, err = d.redis.Do(ctx, "SETEX", key, int(d.cacheExpire), string(data)); err != nil {
		log.Error("AddCacheGetLivePageInterventionCards error(%v), key(%s)", err, key)
		return
	}
	return
}

func keyRecommendedExposureLevels() string {
	return "recommended_exposure_levels"
}

// CacheGetRecommendedExposureLevels get data from cache
func (d *dao) CacheGetRecommendedExposureLevels(ctx context.Context) (levels []*mrecommendedexposurelevel.Model, err error) {
	key := keyRecommendedExposureLevels()
	r, err := d.redis.Do(ctx, "GET", key)
	if err != nil {
		if err == redis.ErrNil {
			err = nil
		}
	}
	if err == nil && r != nil {
		err = json.Unmarshal(r.([]byte), &levels)
		if err != nil {
			log.Error("CacheGetRecommendedExposureLevels error(%v), key(%s)", err, key)
			levels = nil
			err = nil
		}
	}
	if err != nil {
		levels = nil
		log.Error("CacheGetRecommendedExposureLevels error(%v), key(%s)", err, key)
		return
	}
	return
}

// AddCacheGetRecommendedExposureLevels add data to cache
func (d *dao) AddCacheGetRecommendedExposureLevels(ctx context.Context, levels interface{}) (err error) {
	if levels == nil {
		return
	}
	key := keyRecommendedExposureLevels()
	data, err := json.Marshal(levels)
	if err != nil {
		log.Error("AddCacheGetRecommendedExposureLevels error(%v), key(%s)", err, key)
		return
	}
	if _, err = d.redis.Do(ctx, "SETEX", key, int(d.cacheExpire), string(data)); err != nil {
		log.Error("AddCacheGetRecommendedExposureLevels error(%v), key(%s)", err, key)
		return
	}
	return
}
