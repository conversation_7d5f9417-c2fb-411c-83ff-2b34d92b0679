package liverecommendedelements

import (
	"encoding/json"
	"strconv"

	"go-common/library/log"
)

// Model of live_recommended_elements
type Model struct {
	ID             int64       // ID
	Sort           int         // 推荐位排序/位置：0 删除
	ElementID      int64       // 元素 ID
	ElementType    ElementType // 元素类型
	Name           string      // 名称
	Cover          string      // 封面
	URL            string      // 链接
	Attr           uint16      // 位 1 显示关闭按钮
	StartTime      *int64      // 开始时间
	ExpireTime     int64       // 过期时间
	CreateTime     int64       // 创建时间
	ModifiedTime   int64       // 修改时间
	ExtendedFields *string     // 额外数据，JSON format
}

// ExtendedFieldsData 额外字段数据结构
type ExtendedFieldsData struct {
	ExposureLevelID int64 `json:"exposure_level_id"`
	From            int   `json:"from"`
}

// ElementType 元素类型
type ElementType = int

const (
	ElementTypeSquare        ElementType = 5  // 直播广场房间列表推荐，name 转成的数字代表分区，热门 -1，新星 -2
	ElementTypeLiveRecommend ElementType = 6  // 直播推荐排期表
	ElementAlgorithmExposure ElementType = 18 // 热门列表推荐算法曝光干预卡，运营配置直播间特定时段的曝光量
)

const (
	FromOperator = 0 // 运营配置
	FromGuild    = 1 // 公会申请
)

// Attr 位标志常量
const (
	AttrTagNoble = 1 << 3 // 贵族推荐标记
)

func (m *Model) GetCatalogID() *int64 {
	// 根据 name 设置分区 ID
	if m.Name == "" {
		log.Error("RawGetLivePageInterventionCards.Name is empty, card id: %d", m.ID)
		return nil
	}
	nameVal, err := strconv.ParseInt(m.Name, 10, 64)
	if err != nil {
		log.Error("RawGetLivePageInterventionCards.Name is not a number, card id: %d", m.ID)
		return nil
	}
	if nameVal == -1 {
		catalogID := int64(0)
		return &catalogID
	} else if nameVal == -2 {
		catalogID := int64(-1)
		return &catalogID
	}
	return &nameVal
}

// GetExposureLevelID 从 ExtendedFields 中获取 ExposureLevelID
func (m *Model) GetExposureLevelID() int64 {
	if m.ExtendedFields == nil || *m.ExtendedFields == "" {
		return 0
	}

	var data ExtendedFieldsData
	err := json.Unmarshal([]byte(*m.ExtendedFields), &data)
	if err != nil {
		log.Error("Failed to unmarshal ExtendedFields, card: %+v, error: %v", m, err)
		return 0
	}

	return data.ExposureLevelID
}

// GetFrom 从 ExtendedFields 中获取 From 字段
func (m *Model) GetFrom() int {
	if m.ExtendedFields == nil || *m.ExtendedFields == "" {
		return FromOperator // 默认为运营配置
	}

	var data ExtendedFieldsData
	err := json.Unmarshal([]byte(*m.ExtendedFields), &data)
	if err != nil {
		log.Error("Failed to unmarshal ExtendedFields for From, card: %+v, error: %v", m, err)
		return FromOperator // 默认为运营配置
	}

	return data.From
}
