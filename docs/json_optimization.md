# JSON 字段优化说明

## 问题背景

原代码在 SQL 查询中使用 `JSON_EXTRACT` 函数处理 JSON 类型的字段，这可能导致慢查询问题：

1. **m_recommended_elements 表的 more 字段**：使用 `JSON_EXTRACT(m.more, '$.exposure_level_id')`
2. **live_recommended_elements 表的 extended_fields 字段**：使用 `JSON_EXTRACT(l.extended_fields, '$.exposure_level_id')` 和 `JSON_EXTRACT(extended_fields, '$.from')`

## 优化方案

将 JSON 解析从 SQL 层移到应用代码层处理，提高查询性能。

### 1. queryHomeFeedInterventionCardsCount 函数优化

**优化前：**
```sql
AND JSON_EXTRACT(m.more, '$.exposure_level_id') IS NOT NULL
```

**优化后：**
```sql
AND m.more IS NOT NULL
AND m.more != ''
```

移除了 SQL 中的 JSON_EXTRACT 检查，改为在代码中解析 JSON 后过滤。

### 2. queryHomeFeedInterventionCardsList 函数优化

**优化前：**
- SQL 中使用 `JSON_EXTRACT(m.more, '$.exposure_level_id')` 获取曝光等级 ID
- SQL 中使用 `JSON_EXTRACT(l.extended_fields, '$.exposure_level_id')` 获取曝光等级 ID

**优化后：**
- SQL 直接查询 `m.more` 和 `l.extended_fields` 字段
- 在代码中使用 `json.Unmarshal` 解析 JSON 获取 `exposure_level_id`
- 对于 `live_recommended_elements` 的直播推荐排期，直接使用常量 `LiveRecommendScheduleExposureLevelID`

### 3. RawGetLivePageInterventionCards 函数优化

**优化前：**
```sql
AND JSON_EXTRACT(extended_fields, '$.from') = ?
```

**优化后：**
- SQL 中移除 JSON_EXTRACT 检查
- 在代码中添加 `GetFrom()` 方法解析 JSON 获取 `from` 字段
- 在代码逻辑中检查 `from` 字段值

## 代码变更详情

### 1. 数据库查询层 (internal/dao/db.go)

- 移除了所有 SQL 中的 `JSON_EXTRACT` 函数调用
- 修改查询字段，直接获取 JSON 字段原始值
- 在代码中添加 JSON 解析逻辑
- 添加数据过滤逻辑，确保只处理有效的记录

### 2. 模型层 (internal/model/liverecommendedelements/model.go)

- 扩展 `ExtendedFieldsData` 结构，添加 `From` 字段
- 新增 `GetFrom()` 方法，用于从 JSON 中解析 `from` 字段

## 性能优化效果

1. **减少 SQL 复杂度**：移除了 JSON 函数调用，简化了查询语句
2. **提高查询性能**：避免了数据库层的 JSON 解析开销
3. **更好的可维护性**：JSON 解析逻辑集中在代码中，便于调试和优化
4. **保持功能一致性**：优化后的逻辑与原有功能完全一致

## 注意事项

1. **数据过滤**：在代码中需要正确处理 JSON 解析失败的情况
2. **默认值处理**：为 `live_recommended_elements` 的直播推荐排期提供了固定的曝光等级 ID
3. **错误处理**：添加了详细的错误日志，便于问题排查

## 测试建议

建议对以下场景进行测试：
1. 正常的 JSON 数据解析
2. 无效的 JSON 数据处理
3. 空字段处理
4. 性能对比测试（优化前后的查询时间对比）
